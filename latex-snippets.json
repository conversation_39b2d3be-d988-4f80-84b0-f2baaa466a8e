{"Key Point Box": {"prefix": "keypoint", "body": ["\\begin{keypoint}", "$1", "\\end{keypoint}"], "description": "Insert a key point box"}, "Warning Box": {"prefix": "warning", "body": ["\\begin{warning}", "$1", "\\end{warning}"], "description": "Insert a warning box"}, "Tip Box": {"prefix": "tip", "body": ["\\begin{tip}", "$1", "\\end{tip}"], "description": "Insert a tip box"}, "Definition": {"prefix": "def", "body": ["\\begin{definition}", "$1", "\\end{definition}"], "description": "Insert a definition environment"}, "Theorem": {"prefix": "thm", "body": ["\\begin{theorem}", "$1", "\\end{theorem}"], "description": "Insert a theorem environment"}, "Proof": {"prefix": "proof", "body": ["\\begin{proof}", "$1", "\\end{proof}"], "description": "Insert a proof environment"}, "Align Equations": {"prefix": "align", "body": ["\\begin{align}", "    $1 &= $2 \\\\", "    &= $3", "\\end{align}"], "description": "Insert aligned equations"}, "Code Block": {"prefix": "code", "body": ["\\begin{lstlisting}[language=${1:Python}, caption=${2:代码标题}]", "$3", "\\end{lstlisting}"], "description": "Insert a code block"}, "Figure": {"prefix": "fig", "body": ["\\begin{figure}[H]", "    \\centering", "    \\includegraphics[width=${1:0.6}\\textwidth]{${2:filename}}", "    \\caption{${3:图片标题}}", "    \\label{fig:${4:label}}", "\\end{figure}"], "description": "Insert a figure"}, "Table": {"prefix": "table", "body": ["\\begin{table}[H]", "\\centering", "\\caption{${1:表格标题}}", "\\begin{tabular}{@{}${2:lcc}@{}}", "\\toprule", "${3:Header 1} & ${4:Header 2} & ${5:Header 3} \\\\", "\\midrule", "${6:Row 1} & ${7:Data} & ${8:Data} \\\\", "${9:Row 2} & ${10:Data} & ${11:Data} \\\\", "\\bottomrule", "\\end{tabular}", "\\end{table}"], "description": "Insert a table"}, "Important Text": {"prefix": "imp", "body": "\\important{$1}", "description": "Highlight important text"}, "Note Text": {"prefix": "note", "body": "\\note{$1}", "description": "Add a note"}, "Section": {"prefix": "sec", "body": ["\\section{${1:章节标题}}", "", "$2"], "description": "Insert a section"}, "Subsection": {"prefix": "subsec", "body": ["\\subsection{${1:子章节标题}}", "", "$2"], "description": "Insert a subsection"}, "Itemize": {"prefix": "item", "body": ["\\begin{itemize}", "    \\item $1", "    \\item $2", "    \\item $3", "\\end{itemize}"], "description": "Insert an itemized list"}, "Enumerate": {"prefix": "enum", "body": ["\\begin{enumerate}", "    \\item $1", "    \\item $2", "    \\item $3", "\\end{enumerate}"], "description": "Insert an enumerated list"}, "Inline Math": {"prefix": "im", "body": "$$1$", "description": "Insert inline math"}, "Display Math": {"prefix": "dm", "body": ["$$", "$1", "$$"], "description": "Insert display math"}, "Equation": {"prefix": "eq", "body": ["\\begin{equation}", "    $1", "\\end{equation}"], "description": "Insert numbered equation"}}